/**
 * Facebook Automation Desktop - React App Entry Point
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';
import 'antd/dist/reset.css';
import './styles/global.css';

// Add global error handlers
window.addEventListener('error', (event) => {
  console.error('❌ [GLOBAL ERROR]', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('❌ [UNHANDLED PROMISE REJECTION]', event.reason);
});

// Debug logging
console.log('🚀 [INIT] Starting React app...');
console.log('🚀 [INIT] Environment:', process.env.NODE_ENV);
console.log('🚀 [INIT] Electron API available:', !!window.electronAPI);

// Get the root element
const container = document.getElementById('root');
if (!container) {
  console.error('❌ [INIT] Root element not found!');
  document.body.innerHTML = '<div style="padding: 20px; color: red; font-family: monospace;">ERROR: Root element not found!</div>';
} else {
  console.log('✅ [INIT] Root element found');

  try {
    // Temporarily bypass authentication check and show login modal directly
    console.log('🔍 [AUTH] Bypassing auth check, showing login modal directly...');
    showLoginModal();

    function showDashboard(userData) {
      console.log('🔍 [DASHBOARD] Showing dashboard for user:', userData.user.email);
      container.innerHTML = `
        <div class="App" style="padding: 20px; font-family: Arial, sans-serif;">
          <h1>🚀 Facebook Automation Desktop</h1>
          <div style="background: #f0f0f0; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Welcome, ${userData.user.name || userData.user.email}!</h3>
            <p>Email: ${userData.user.email}</p>
            <p>Role: ${userData.user.role || 'user'}</p>
          </div>
          <div style="margin: 20px 0;">
            <button onclick="logout()" style="padding: 10px 20px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;">Logout</button>
          </div>
          <div style="margin: 20px 0;">
            <h3>Available Features:</h3>
            <ul>
              <li>Facebook Post Scraping</li>
              <li>Bulk Messaging</li>
              <li>Profile Management</li>
            </ul>
          </div>
        </div>
      `;

      // Add logout function to global scope
      window.logout = () => {
        console.log('🔍 [AUTH] Logging out...');
        fetch('http://localhost:3000/logout', {
          method: 'POST',
          credentials: 'include',
        })
        .then(() => {
          console.log('✅ [AUTH] Logged out successfully');
          showLoginModal();
        })
        .catch(error => {
          console.error('❌ [AUTH] Logout error:', error);
          showLoginModal();
        });
      };
    }

    function showLoginModal() {
      console.log('🔍 [LOGIN] Showing login modal');
      container.innerHTML = `
        <div class="App" style="padding: 20px; font-family: Arial, sans-serif;">
          <div style="max-width: 400px; margin: 50px auto; background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            <h2 style="text-align: center; margin-bottom: 30px;">🚀 Login to Facebook Automation</h2>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px;">Email:</label>
              <input type="email" id="email" placeholder="Enter your email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
              <label style="display: block; margin-bottom: 5px;">Password:</label>
              <input type="password" id="password" placeholder="Enter your password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
            </div>

            <div style="margin-bottom: 20px;">
              <button onclick="loginWithEmail()" style="width: 100%; padding: 12px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">Login with Email</button>
            </div>

            <div style="margin-bottom: 20px;">
              <button onclick="loginWithGoogle()" style="width: 100%; padding: 12px; background: #db4437; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;">Login with Google</button>
            </div>

            <div id="error-message" style="color: red; text-align: center; margin-top: 10px;"></div>
            <div id="success-message" style="color: green; text-align: center; margin-top: 10px;"></div>
          </div>
        </div>
      `;

      // Add login functions to global scope
      window.loginWithEmail = () => {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        const errorDiv = document.getElementById('error-message');
        const successDiv = document.getElementById('success-message');

        if (!email || !password) {
          errorDiv.textContent = 'Please enter both email and password';
          return;
        }

        console.log('🔍 [LOGIN] Attempting email login for:', email);
        errorDiv.textContent = '';
        successDiv.textContent = 'Logging in...';

        fetch('http://localhost:3000/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ email, password }),
        })
        .then(response => {
          if (response.ok) {
            return response.json();
          } else {
            throw new Error('Invalid credentials');
          }
        })
        .then(userData => {
          console.log('✅ [LOGIN] Email login successful:', userData);
          successDiv.textContent = 'Login successful! Redirecting...';
          setTimeout(() => showDashboard(userData), 1000);
        })
        .catch(error => {
          console.error('❌ [LOGIN] Email login error:', error);
          errorDiv.textContent = 'Invalid email or password';
          successDiv.textContent = '';
        });
      };

      window.loginWithGoogle = () => {
        console.log('🔍 [LOGIN] Starting Google OAuth...');
        const errorDiv = document.getElementById('error-message');
        const successDiv = document.getElementById('success-message');

        if (window.electronAPI) {
          // For Electron app, we need a different approach
          // Since external browser cookies don't share with Electron

          errorDiv.textContent = '';
          successDiv.textContent = 'Opening Google login in your browser...';

          // Generate a unique session ID for this login attempt
          const sessionId = 'desktop_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
          console.log('🔍 [LOGIN] Generated session ID:', sessionId);

          // Open external browser with session ID
          const authUrl = `http://localhost:3000/auth/google?session_id=${sessionId}&return_url=http://localhost:3000/auth/success`;
          console.log('🔍 [LOGIN] Opening external browser for OAuth:', authUrl);

          window.electronAPI.openExternal(authUrl);

          successDiv.textContent = 'Please complete the login in your browser. We will automatically detect when you are logged in.';

          // Start polling for authentication status using session ID
          let pollCount = 0;
          const maxPolls = 150; // 5 minutes (150 * 2 seconds)

          const pollInterval = setInterval(() => {
            pollCount++;
            console.log(`🔍 [LOGIN] Polling for auth status... (${pollCount}/${maxPolls})`);

            // Try to get auth status using session ID
            fetch(`http://localhost:3000/auth/verify?sessionId=${sessionId}`, {
              method: 'GET',
              credentials: 'include',
            })
            .then(response => {
              if (response.ok) {
                return response.json();
              } else {
                throw new Error('Session not found or not authenticated yet');
              }
            })
            .then(sessionData => {
              console.log('✅ [LOGIN] Session authenticated:', sessionData);
              clearInterval(pollInterval);

              // Now get user data and set cookies for Electron
              if (sessionData.access_token) {
                // Set the auth token in a way Electron can use
                document.cookie = `access_token=${sessionData.access_token}; path=/; max-age=3600`;

                successDiv.textContent = 'Login successful! Redirecting...';
                setTimeout(() => showDashboard(sessionData), 1000);
              } else {
                throw new Error('No access token in session data');
              }
            })
            .catch(error => {
              console.log('🔍 [LOGIN] Still waiting for OAuth completion...');

              if (pollCount >= maxPolls) {
                clearInterval(pollInterval);
                errorDiv.textContent = 'OAuth timeout. Please try logging in with email/password or try Google login again.';
                successDiv.textContent = '';
              }
            });
          }, 2000); // Poll every 2 seconds

        } else {
          // For web browser, redirect directly
          window.location.href = 'http://localhost:3000/auth/google';
        }
      };
    }
  } catch (error) {
    console.error('❌ [INIT] Failed to render app:', error);
    container.innerHTML = `
      <div style="padding: 20px; color: red; font-family: monospace;">
        <h2>React Initialization Error</h2>
        <p>Error: ${error.message}</p>
        <p>Check console for details</p>
      </div>
    `;
  }
}
