# Dependencies
node_modules/
backend/venv/
backend/__pycache__/
*.pyc
*.pyo
*.pyd
__pycache__/
*.so

# Build outputs
dist/
build/
src/renderer/dist/
backend/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
backend/data/logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Application specific
backend/data/
facebook_automation.db
*.db
*.sqlite

# Browser profiles
profiles/
user-data-dir/

# Exports
exports/
*.xlsx
*.csv

# Screenshots
screenshots/
*.png
*.jpg
*.jpeg

# Electron
out/
app/
packages/

# Python
*.egg-info/
.pytest_cache/
.coverage
htmlcov/

# Redis
dump.rdb

# Certificates
*.pem
*.key
*.crt

augment-vip/