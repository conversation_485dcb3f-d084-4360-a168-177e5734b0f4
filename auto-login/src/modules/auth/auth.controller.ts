import {
  Controller,
  Post,
  Body,
  Get,
  Render,
  UseGuards,
  Req,
  Query,
  Res,
  Param,
  UnauthorizedException,
  HttpException,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { ChangePasswordDto } from './dto/change-password.dto';
import { ConfigService } from '@nestjs/config';
import { JwtAuthGuard } from './jwt-auth.guard';
import { RolesGuard } from './roles.guard';
import { Roles } from './roles.decorator';

@Controller()
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {}

  @Get('login')
  @Render('auth/login')
  showLogin() {
    return {};
  }

  @Post('register')
  register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @Post('login')
  async login(@Body() loginDto: LoginDto, @Res() res) {
    const { access_token, refresh_token } =
      await this.authService.login(loginDto);

    // Set HTTP-only cookies
    // Access token cookie - short lived
    res.cookie('access_token', access_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 3600000, // 1 hour
      domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
    });

    // Refresh token cookie - longer lived
    res.cookie('refresh_token', refresh_token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      maxAge: 7 * 24 * 3600000, // 7 days
      path: '/auth', // Only available to /auth routes for security
      domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
    });

    return res.json({ success: true });
  }

  @Post('auth/refresh')
  async refreshToken(@Req() req, @Res() res) {
    try {
      const refreshToken = req.cookies['refresh_token'];

      if (!refreshToken) {
        throw new UnauthorizedException('Refresh token not found');
      }

      const { access_token, refresh_token } =
        await this.authService.refreshToken(refreshToken);

      // Set new HTTP-only cookies
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      return res.json({ success: true });
    } catch (error) {
      // Clear cookies on error with same options as when setting them
      res.clearCookie('access_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        domain: process.env.COOKIE_DOMAIN || undefined,
      });

      res.clearCookie('refresh_token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        path: '/auth',
        domain: process.env.COOKIE_DOMAIN || undefined,
      });

      return res.status(401).json({
        success: false,
        message: error.message || 'Invalid refresh token',
      });
    }
  }

  @Get('auth/status')
  async getAuthStatus(@Req() req, @Res() res) {
    try {
      const token = req.cookies['access_token'];
      if (!token) {
        return res.json({
          isAuthenticated: false,
          user: null,
          status: null,
        });
      }

      const decoded = await this.authService.verifyToken(token);
      return res.json({
        isAuthenticated: true,
        user: decoded,
      });
    } catch (error) {
      return res.json({
        isAuthenticated: false,
        user: null,
        status: null,
      });
    }
  }

  @Get('auth/google')
  @UseGuards(AuthGuard('google'))
  async googleAuth(
    @Query('return_url') returnUrl: string,
    @Query('session_id') sessionId: string,
    @Req() req: any,
  ) {
    // Store return URL and session ID in request for callback
    if (returnUrl) {
      req.session.returnUrl = returnUrl;
    }
    if (sessionId) {
      req.session.sessionId = sessionId;
    }
    return { returnUrl, sessionId };
  }

  @Get('auth/discord')
  @UseGuards(AuthGuard('discord'))
  async discordAuth(@Query('return_url') returnUrl?: string) {
    // Store return URL in session for callback
    if (returnUrl) {
      // You might want to validate the return URL here
      return { returnUrl };
    }
  }

  @Post('logout')
  async logout(@Res() res) {
    // Clear both cookies with same options as when setting them
    res.clearCookie('access_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    res.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
      path: '/auth',
      domain: process.env.COOKIE_DOMAIN || undefined,
    });

    return res.json({ success: true });
  }

  @Get('auth/google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthCallback(@Req() req, @Res() res) {
    try {
      const { access_token, refresh_token } =
        await this.authService.validateGoogleLogin(req.user);

      // Set HTTP-only cookies for desktop app
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax',
        maxAge: 3600000, // 1 hour
        domain: 'localhost', // Explicitly set to localhost for desktop
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: false, // Set to false for localhost
        sameSite: 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: 'localhost', // Explicitly set to localhost for desktop
      });

      // If there's a session ID, store the auth data for desktop app polling
      const sessionId = req.session?.sessionId;
      if (sessionId) {
        console.log('🔍 [AUTH] Storing session data for desktop app:', sessionId);
        // Store session data in memory (you might want to use Redis for production)
        if (!global.desktopSessions) {
          global.desktopSessions = new Map();
        }
        global.desktopSessions.set(sessionId, {
          access_token,
          refresh_token,
          user: req.user,
          timestamp: Date.now(),
        });

        // Clean up old sessions (older than 10 minutes)
        const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
        for (const [key, value] of global.desktopSessions.entries()) {
          if (value.timestamp < tenMinutesAgo) {
            global.desktopSessions.delete(key);
          }
        }
      }

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .success { color: #52c41a; font-size: 24px; margin-bottom: 20px; }
              .instructions { color: #666; font-size: 16px; }
            </style>
          </head>
          <body>
            <div class="success">✅ Login Successful!</div>
            <div class="instructions">
              You can now return to the desktop application and try logging in again.
              <br><br>
              This window will close automatically in 3 seconds.
            </div>
            <script>
              // Send success message to opener window
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Auto close after 3 seconds
              setTimeout(() => {
                window.close();
              }, 3000);
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

                window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('session/:sessionId')
  async getSessionData(@Param('sessionId') sessionId: string) {
    console.log('🔍 [AUTH] Checking session data for:', sessionId);

    if (!global.desktopSessions) {
      throw new NotFoundException('Session not found');
    }

    const sessionData = global.desktopSessions.get(sessionId);
    if (!sessionData) {
      throw new NotFoundException('Session not found');
    }

    // Check if session is still valid (not older than 10 minutes)
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000;
    if (sessionData.timestamp < tenMinutesAgo) {
      global.desktopSessions.delete(sessionId);
      throw new NotFoundException('Session expired');
    }

    console.log('✅ [AUTH] Session data found for:', sessionId);

    // Return session data and remove it (one-time use)
    global.desktopSessions.delete(sessionId);

    return {
      access_token: sessionData.access_token,
      refresh_token: sessionData.refresh_token,
      user: sessionData.user,
    };
  }

  @Get('auth/success')
  async authSuccess(@Res() res) {
    return res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Successful</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
            .success { color: #52c41a; font-size: 28px; margin-bottom: 20px; }
            .instructions { color: #666; font-size: 16px; line-height: 1.5; }
            .button { background: #1890ff; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-top: 20px; }
            .button:hover { background: #40a9ff; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="success">✅ Login Successful!</div>
            <div class="instructions">
              Your Google account has been successfully authenticated.
              <br><br>
              You can now return to the desktop application and use the regular login form with your email and password.
              <br><br>
              <strong>Note:</strong> You may need to refresh the desktop app or try logging in again.
            </div>
            <button class="button" onclick="window.close()">Close Window</button>
          </div>
        </body>
      </html>
    `);
  }

  @Get('auth/error')
  async authError(@Query('message') message: string, @Res() res) {
    return res.send(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Authentication Failed</title>
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f5f5f5; }
            .container { background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 500px; margin: 0 auto; }
            .error { color: #ff4d4f; font-size: 28px; margin-bottom: 20px; }
            .instructions { color: #666; font-size: 16px; line-height: 1.5; }
            .button { background: #ff4d4f; color: white; padding: 12px 24px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; margin-top: 20px; }
            .button:hover { background: #ff7875; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="error">❌ Authentication Failed</div>
            <div class="instructions">
              There was an error during the authentication process.
              <br><br>
              Error: ${message || 'Unknown error'}
              <br><br>
              Please try again or contact support if the problem persists.
            </div>
            <button class="button" onclick="window.close()">Close Window</button>
          </div>
        </body>
      </html>
    `);
  }

  @Get('auth/discord/callback')
  @UseGuards(AuthGuard('discord'))
  async discordAuthCallback(@Req() req, @Res() res) {
    try {
      const { access_token, refresh_token } =
        await this.authService.validateDiscordLogin(req.user);

      // Set HTTP-only cookies
      // Access token cookie - short lived
      res.cookie('access_token', access_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 3600000, // 1 hour
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      // Refresh token cookie - longer lived
      res.cookie('refresh_token', refresh_token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 3600000, // 7 days
        path: '/auth', // Only available to /auth routes for security
        domain: process.env.COOKIE_DOMAIN || undefined, // Use COOKIE_DOMAIN env var or undefined
      });

      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Successful</title>
          </head>
          <body>
            <script>
              // Send success message to opener window
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_SUCCESS' }, '*');
              }

              // Close this window
              window.close();
            </script>
          </body>
        </html>
      `);
    } catch (error) {
      return res.send(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Authentication Failed</title>
          </head>
          <body>
            <script>
              if (window.opener && !window.opener.closed) {
                window.opener.postMessage({ type: 'AUTH_FAILED' }, '*');
              }

              window.close();
            </script>
          </body>
        </html>
      `);
    }
  }

  @Get('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @Render('auth/change-password')
  showChangePassword() {
    return {};
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  async changePassword(
    @Body() changePasswordDto: ChangePasswordDto,
    @Req() req,
  ) {
    try {
      const userId = req.user.sub;
      return await this.authService.changePassword(userId, changePasswordDto);
    } catch (error) {
      throw new HttpException(
        error.message || 'Failed to change password',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('auth/verify')
  async verifyToken(@Req() req: any, @Res() res: any) {
    try {
      console.log('🔍 [AUTH] Verify token request received');
      console.log('🔍 [AUTH] Cookies:', req.cookies);
      console.log('🔍 [AUTH] Headers:', req.headers);

      const token = req.cookies['access_token'];

      if (!token) {
        console.log('❌ [AUTH] No access token found in cookies');
        return res.status(401).json({
          valid: false,
          message: 'No access token found',
        });
      }

      console.log('🔍 [AUTH] Token found, verifying...');
      const decoded = await this.authService.verifyToken(token);

      console.log('✅ [AUTH] Token verified successfully:', decoded);

      return res.json({
        valid: true,
        user: {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
          status: decoded.status,
        },
        message: 'Token is valid',
      });
    } catch (error) {
      console.error('❌ [AUTH] Token verification failed:', error);
      return res.status(401).json({
        valid: false,
        message: 'Invalid token',
        error: error.message,
      });
    }
  }
}
